{"rustc": 2830703817519440116, "features": "[\"dashmap\", \"default\", \"futures\", \"futures-timer\", \"jitter\", \"quanta\", \"rand\", \"std\"]", "declared_features": "[\"dashmap\", \"default\", \"futures\", \"futures-timer\", \"jitter\", \"no_std\", \"quanta\", \"rand\", \"std\"]", "target": 12176594082583514360, "profile": 15657897354478470176, "path": 1103814642591179341, "deps": [[2706460456408817945, "futures", false, 7186115126148961836], [2828590642173593838, "cfg_if", false, 5608852898148831518], [3666196340704888985, "smallvec", false, 2673802910270356896], [3958489542916937055, "portable_atomic", false, 1883991800582992664], [4495526598637097934, "parking_lot", false, 13893717740496584092], [5364813825765636762, "dashmap", false, 16683981598846165880], [6613228103912905794, "nonzero_ext", false, 14769604338283076966], [8140693133181067772, "futures_timer", false, 17673974632701604162], [9108455738564554921, "quanta", false, 1138047484631683897], [11229226719215837303, "spinning_top", false, 4479988585288326947], [13208667028893622512, "rand", false, 9577676731279368259], [13410323020980513144, "no_std_compat", false, 502100428474026802]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\governor-f12efb26ba157fdd\\dep-lib-governor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}