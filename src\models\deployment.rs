use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Application {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub name: String,
    pub description: Option<String>,
    pub repository: Repository,
    pub environment: Environment,
    pub runtime_config: RuntimeConfig,
    pub status: ApplicationStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub last_deployed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Repository {
    pub provider: GitProvider,
    pub url: String,
    pub branch: String,
    pub auto_deploy: bool,
    pub webhook_secret: Option<String>,
    pub access_token: Option<String>, // Encrypted
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GitProvider {
    GitHub,
    GitLab,
    Bitbucket,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Environment {
    pub name: String,
    pub variables: HashMap<String, String>, // Encrypted
    pub secrets: HashMap<String, String>,   // Encrypted
    pub build_command: Option<String>,
    pub start_command: String,
    pub dockerfile_path: Option<String>,
    pub root_directory: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuntimeConfig {
    pub service_type: ServiceType,
    pub instance_type: String,
    pub region: String,
    pub plan: Option<String>,
    pub instances: Option<u32>,
    pub memory_mb: Option<u32>,
    pub auto_scaling: Option<AutoScalingConfig>,
    pub health_check: Option<HealthCheckConfig>,
    pub networking: Option<NetworkingConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceType {
    WebService,
    BackgroundWorker,
    CronJob { schedule: String },
    StaticSite,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoScalingConfig {
    pub enabled: bool,
    pub min_instances: u32,
    pub max_instances: u32,
    pub target_cpu_percent: f32,
    pub target_memory_percent: f32,
    pub scale_up_cooldown: u32,   // seconds
    pub scale_down_cooldown: u32, // seconds
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub path: String,
    pub port: u16,
    pub interval: u32,    // seconds
    pub timeout: u32,     // seconds
    pub retries: u32,
    pub grace_period: u32, // seconds
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkingConfig {
    pub port: u16,
    pub custom_domains: Vec<String>,
    pub ssl_enabled: bool,
    pub internal_networking: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApplicationStatus {
    Creating,
    Building,
    Deploying,
    Running,
    Stopped,
    Failed,
    Suspended,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Deployment {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub application_id: ObjectId,
    pub user_id: ObjectId,
    pub status: DeploymentStatus,
    #[serde(with = "datetime_as_bson_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub updated_at: DateTime<Utc>,
    #[serde(with = "optional_datetime_as_bson_datetime", default, skip_serializing_if = "Option::is_none")]
    pub deployed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeploymentStatus {
    Pending,
    Queued,
    Building,
    Deploying,
    Live,
    Failed,
    Cancelled,
    RolledBack,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeploymentTrigger {
    Manual { user_id: ObjectId },
    GitPush { commit_sha: String },
    Webhook { source: String },
    Rollback { from_deployment_id: ObjectId },
    AutoScale { reason: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildLog {
    #[serde(with = "datetime_as_bson_datetime")]
    pub timestamp: DateTime<Utc>,
    pub level: LogLevel,
    pub message: String,
    pub step: BuildStep,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BuildStep {
    Clone,
    Dependencies,
    Build,
    Test,
    Package,
    Deploy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuntimeLog {
    #[serde(with = "datetime_as_bson_datetime")]
    pub timestamp: DateTime<Utc>,
    pub level: LogLevel,
    pub message: String,
    pub source: LogSource,
    pub instance_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
    Fatal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogSource {
    Application,
    System,
    LoadBalancer,
    Database,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentMetrics {
    pub build_duration: Option<u64>, // seconds
    pub deploy_duration: Option<u64>, // seconds
    pub instances_count: u32,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub request_count: u64,
    pub error_rate: f32,
    pub response_time_p95: f32, // milliseconds
}

// Request/Response DTOs
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateApplicationRequest {
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    pub description: Option<String>,
    pub repository: CreateRepositoryRequest,
    pub environment: CreateEnvironmentRequest,
    pub runtime_config: CreateRuntimeConfigRequest,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateRepositoryRequest {
    pub provider: GitProvider,
    #[validate(url)]
    pub url: String,
    #[validate(length(min = 1))]
    pub branch: String,
    pub auto_deploy: bool,
    pub access_token: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateEnvironmentRequest {
    #[validate(length(min = 1))]
    pub name: String,
    pub variables: HashMap<String, String>,
    pub secrets: HashMap<String, String>,
    pub build_command: Option<String>,
    #[validate(length(min = 1))]
    pub start_command: String,
    pub dockerfile_path: Option<String>,
    pub root_directory: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateRuntimeConfigRequest {
    pub service_type: ServiceType,
    pub instance_type: String,
    pub region: String,
    pub auto_scaling: AutoScalingConfig,
    pub health_check: HealthCheckConfig,
    pub networking: NetworkingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub repository: Repository,
    pub environment: EnvironmentResponse,
    pub runtime_config: RuntimeConfig,
    pub status: ApplicationStatus,
    pub url: Option<String>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub updated_at: DateTime<Utc>,
    #[serde(with = "optional_datetime_as_bson_datetime", default, skip_serializing_if = "Option::is_none")]
    pub last_deployed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentResponse {
    pub name: String,
    pub variables: HashMap<String, String>, // Values hidden for security
    pub secrets: Vec<String>,               // Only keys, values hidden
    pub build_command: Option<String>,
    pub start_command: String,
    pub dockerfile_path: Option<String>,
    pub root_directory: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentResponse {
    pub id: String,
    pub application_id: String,
    pub status: DeploymentStatus,
    #[serde(with = "datetime_as_bson_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub updated_at: DateTime<Utc>,
    #[serde(with = "optional_datetime_as_bson_datetime", default, skip_serializing_if = "Option::is_none")]
    pub deployed_at: Option<DateTime<Utc>>,
    pub url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct TriggerDeploymentRequest {
    pub branch: Option<String>,
    pub commit_sha: Option<String>,
    pub force_rebuild: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitWebhookPayload {
    pub repository: GitRepositoryInfo,
    pub commits: Vec<GitCommit>,
    pub ref_name: String,
    pub before: String,
    pub after: String,
    pub pusher: GitUser,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitRepositoryInfo {
    pub id: u64,
    pub name: String,
    pub full_name: String,
    pub clone_url: String,
    pub default_branch: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitCommit {
    pub id: String,
    pub message: String,
    #[serde(with = "datetime_as_bson_datetime")]
    pub timestamp: DateTime<Utc>,
    pub author: GitUser,
    pub added: Vec<String>,
    pub removed: Vec<String>,
    pub modified: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitUser {
    pub name: String,
    pub email: String,
    pub username: Option<String>,
}
