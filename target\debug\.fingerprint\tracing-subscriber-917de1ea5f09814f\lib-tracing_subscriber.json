{"rustc": 2830703817519440116, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 6355579909791343455, "path": 10592228219403531104, "deps": [[1009387600818341822, "matchers", false, 16859081211847830776], [1017461770342116999, "sharded_slab", false, 11113049063287206901], [1359731229228270592, "thread_local", false, 3598157531922693052], [3424551429995674438, "tracing_core", false, 4904015800900561561], [3666196340704888985, "smallvec", false, 2673802910270356896], [3722963349756955755, "once_cell", false, 15293267452495451070], [6981130804689348050, "tracing_serde", false, 2629224706915007169], [8606274917505247608, "tracing", false, 17320339988540626530], [8614575489689151157, "nu_ansi_term", false, 8426664133511169309], [9451456094439810778, "regex", false, 3993220404771854576], [9689903380558560274, "serde", false, 17239657888703164653], [10806489435541507125, "tracing_log", false, 14258795699243274997], [15367738274754116744, "serde_json", false, 4289499926161375560]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-917de1ea5f09814f\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}