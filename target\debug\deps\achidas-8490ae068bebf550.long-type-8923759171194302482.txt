fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<models::deployment::CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {update_application}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<models::deployment::CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {update_application}: Handler<_, _>
