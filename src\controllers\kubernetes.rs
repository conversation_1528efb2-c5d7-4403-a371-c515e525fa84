use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, Controller<PERSON><PERSON><PERSON>},
    models::ApiResponse,
    services::kubernetes_deployment::{
        KubernetesDeploymentService, KubernetesNode, KubernetesNodeRequest, ClusterCapacity
    },
    AppState,
};
use axum::{
    extract::{Path, State},
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::instrument;

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateNodeRequest {
    pub region: String,
    pub pool_type: String, // "hot" or "cold"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NodeStatusResponse {
    pub instance_id: String,
    pub status: String,
    pub ready: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClusterStatusResponse {
    pub nodes: Vec<KubernetesNode>,
    pub capacity: ClusterCapacity,
    pub health: ClusterHealth,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClusterHealth {
    pub healthy_nodes: u32,
    pub total_nodes: u32,
    pub cpu_utilization: f64,
    pub memory_utilization: f64,
    pub pod_utilization: f64,
}

/// Create a new Kubernetes node
#[instrument(skip(state))]
pub async fn create_node(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateNodeRequest>,
) -> ControllerResult<Json<ApiResponse<KubernetesNode>>> {
    let deployment_service = KubernetesDeploymentService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let node = match request.pool_type.as_str() {
        "hot" => {
            deployment_service
                .create_hot_pool_node(&request.region)
                .await
                .map_err(|e| ControllerError::ExternalApi(format!("Failed to create hot pool node: {}", e)))?
        }
        "cold" => {
            deployment_service
                .create_cold_pool_node(&request.region)
                .await
                .map_err(|e| ControllerError::ExternalApi(format!("Failed to create cold pool node: {}", e)))?
        }
        _ => {
            return Err(ControllerError::BadRequest(
                "Invalid pool_type. Must be 'hot' or 'cold'".to_string(),
            ));
        }
    };

    Ok(success_response(node))
}

/// Get node status
#[instrument(skip(state))]
pub async fn get_node_status(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<NodeStatusResponse>>> {
    let deployment_service = KubernetesDeploymentService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let status = deployment_service
        .get_node_status(&instance_id)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get node status: {}", e)))?;

    let response = NodeStatusResponse {
        instance_id,
        ready: status == "active",
        status,
    };

    Ok(success_response(response))
}

/// Get cluster status and capacity
#[instrument(skip(state))]
pub async fn get_cluster_status(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<ClusterStatusResponse>>> {
    // For now, return a mock response since we need to implement node storage
    // In a real implementation, you'd fetch nodes from your database
    let nodes = vec![];
    
    let deployment_service = KubernetesDeploymentService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let capacity = deployment_service
        .calculate_cluster_capacity(&nodes)
        .await
        .map_err(|e| ControllerError::InternalServerError(format!("Failed to calculate capacity: {}", e)))?;

    let health = ClusterHealth {
        healthy_nodes: nodes.iter().filter(|n| n.status == "active").count() as u32,
        total_nodes: nodes.len() as u32,
        cpu_utilization: 0.0, // Would be calculated from metrics
        memory_utilization: 0.0,
        pod_utilization: 0.0,
    };

    let response = ClusterStatusResponse {
        nodes,
        capacity,
        health,
    };

    Ok(success_response(response))
}

/// Create the first vhf-1c-1gb hot pool node
#[instrument(skip(state))]
pub async fn create_first_hot_node(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<KubernetesNode>>> {
    let deployment_service = KubernetesDeploymentService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    // Use a default region (you can make this configurable)
    let region = "ewr"; // New York/New Jersey region

    let node = deployment_service
        .create_hot_pool_node(region)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to create first hot node: {}", e)))?;

    Ok(success_response(node))
}

/// Delete a Kubernetes node
#[instrument(skip(state))]
pub async fn delete_node(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // First, remove the node from Kubernetes cluster (would need kubectl access)
    // Then delete the Vultr instance
    
    state
        .vultr_client
        .delete_instance(&instance_id)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to delete instance: {}", e)))?;

    Ok(success_response(()))
}

/// Get cost analysis for current cluster
#[instrument(skip(state))]
pub async fn get_cluster_costs(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<ClusterCostAnalysis>>> {
    // Mock implementation - in reality, you'd fetch actual nodes and calculate costs
    let cost_analysis = ClusterCostAnalysis {
        monthly_infrastructure_cost: 6.00, // Starting with one vhf-1c-1gb node
        estimated_monthly_revenue: 0.00,   // No users yet
        estimated_monthly_profit: -6.00,   // Negative until we get users
        break_even_users: 2,
        current_capacity_users: 40,
        profit_at_capacity: 155.28,
        cost_per_user: 0.15, // $6 / 40 users
        nodes_breakdown: vec![
            NodeCostBreakdown {
                node_type: "vhf-1c-1gb".to_string(),
                count: 1,
                monthly_cost_per_node: 6.00,
                total_monthly_cost: 6.00,
                purpose: "Hot pool - high priority workloads".to_string(),
            }
        ],
    };

    Ok(success_response(cost_analysis))
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClusterCostAnalysis {
    pub monthly_infrastructure_cost: f64,
    pub estimated_monthly_revenue: f64,
    pub estimated_monthly_profit: f64,
    pub break_even_users: u32,
    pub current_capacity_users: u32,
    pub profit_at_capacity: f64,
    pub cost_per_user: f64,
    pub nodes_breakdown: Vec<NodeCostBreakdown>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NodeCostBreakdown {
    pub node_type: String,
    pub count: u32,
    pub monthly_cost_per_node: f64,
    pub total_monthly_cost: f64,
    pub purpose: String,
}
