fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::<PERSON><PERSON><CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::<PERSON><PERSON><CreateEnvironmentGroupRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {create_environment_group}: Handler<_, _>
