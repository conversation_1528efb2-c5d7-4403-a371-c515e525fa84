use serde::{Deserialize, Serialize};
use axum::{
    async_trait,
    extract::{FromRequestParts, TypedHeader},
    headers::{authorization::Bearer, Authorization},
    http::request::Parts,
    RequestPartsExt,
};
use jsonwebtoken::{decode, Decoding<PERSON>ey, Validation, Algorithm};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub sub: String,
    pub exp: usize,
    pub iat: usize,
    pub email: Option<String>,
    pub role: Option<String>,
}

#[async_trait]
impl<S> FromRequestParts<S> for Claims
where
    S: Send + Sync,
{
    type Rejection = AuthError;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // Extract the authorization header
        let TypedHeader(Authorization(bearer)) = parts
            .extract::<TypedHeader<Authorization<Bearer>>>()
            .await
            .map_err(|_| AuthError::MissingToken)?;

        // For now, just create a dummy claims object
        // In production, you would decode and validate the JWT token
        let token = bearer.token();
        
        // Simple validation - in production use proper JWT validation
        if token.is_empty() {
            return Err(AuthError::InvalidToken);
        }

        // Return dummy claims for now
        Ok(Claims {
            sub: "user123".to_string(),
            exp: 9999999999, // Far future
            iat: 1000000000, // Past
            email: Some("<EMAIL>".to_string()),
            role: Some("user".to_string()),
        })
    }
}

#[derive(Debug)]
pub enum AuthError {
    MissingToken,
    InvalidToken,
    TokenExpired,
}

impl std::fmt::Display for AuthError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuthError::MissingToken => write!(f, "Missing authorization token"),
            AuthError::InvalidToken => write!(f, "Invalid authorization token"),
            AuthError::TokenExpired => write!(f, "Authorization token expired"),
        }
    }
}

impl std::error::Error for AuthError {}

impl axum::response::IntoResponse for AuthError {
    fn into_response(self) -> axum::response::Response {
        use axum::http::StatusCode;
        use axum::Json;
        use serde_json::json;

        let (status, message) = match self {
            AuthError::MissingToken => (StatusCode::UNAUTHORIZED, "Missing authorization token"),
            AuthError::InvalidToken => (StatusCode::UNAUTHORIZED, "Invalid authorization token"),
            AuthError::TokenExpired => (StatusCode::UNAUTHORIZED, "Authorization token expired"),
        };

        (status, Json(json!({
            "error": message,
            "status": status.as_u16()
        }))).into_response()
    }
}
