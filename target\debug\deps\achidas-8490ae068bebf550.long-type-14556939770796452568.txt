fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {get_environment_group}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<EnvironmentGroupResponse>>, ControllerError>> {get_environment_group}: Handler<_, _>
