# Achidas Intelligent Hosting Platform - Deployment Guide

## Overview

The Achidas Intelligent Hosting Platform is designed to work with **manually provisioned Vultr servers** that are then imported and managed programmatically. This approach gives you full control over server provisioning while leveraging intelligent resource management.

## 🚀 Quick Start

### Step 1: Manually Provision Vultr Servers

1. **Log into your Vultr account**
2. **Deploy servers based on your needs:**

#### Recommended Server Configurations:

**For Shared Hosting (Hot Pool):**
- **Plan**: `vhf-1c-1gb` (High Frequency)
- **Cost**: $6/month
- **Capacity**: 100 users
- **Use Case**: Active shared hosting workloads

**For Shared Hosting (Cold Pool):**
- **Plan**: `vc2-1c-1gb` (Regular Performance)
- **Cost**: $5/month  
- **Capacity**: 120 users
- **Use Case**: Inactive/sleeping workloads

**For Dedicated Hosting:**
- **Plan**: `vc2-2c-4gb`, `vc2-4c-8gb`, `vc2-8c-16gb`
- **Cost**: $12-48/month
- **Capacity**: 1 user per server
- **Use Case**: Isolated VM hosting

**For Enterprise Hosting:**
- **Plan**: `vc2-8c-16gb`, `vc2-16c-32gb`, `vc2-32c-64gb`
- **Cost**: $48-192/month
- **Capacity**: 1 enterprise client
- **Use Case**: Mission-critical applications

### Step 2: Import Servers into Achidas Platform

#### Option A: Interactive Import (Recommended)
```bash
cd deployment
./deploy_first_server.sh
```

#### Option B: List Available Servers
```bash
./deploy_first_server.sh list
```

#### Option C: Direct Import
```bash
./deploy_first_server.sh <instance_id> <pool_type>
# Example:
./deploy_first_server.sh abc123def456 shared-hot
```

### Step 3: Setup Imported Servers

After importing, you'll receive a setup script. Execute it on your server:

```bash
# Copy setup script to your server
scp setup_server.sh root@<your-server-ip>:/root/

# SSH and run setup
ssh root@<your-server-ip>
chmod +x /root/setup_server.sh
./setup_server.sh
```

## 📊 Hosting Plans & Pricing

### Shared Hosting Plans
| Plan | Price (USD) | Price (NGN) | CPU Shares | Memory | Storage | Bandwidth |
|------|-------------|-------------|------------|---------|---------|-----------|
| Starter | $0.99 | ₦1,485 | 256 | 8Mi | 500MB | 5GB |
| Nano | $1.44 | ₦2,160 | 512 | 10Mi | 1GB | 10GB |
| Micro | $2.88 | ₦4,320 | 1024 | 20Mi | 2GB | 25GB |
| Small | $5.76 | ₦8,640 | 2048 | 40Mi | 5GB | 50GB |
| Business | $11.52 | ₦17,280 | 4096 | 80Mi | 10GB | 100GB |

### Dedicated Hosting Plans
| Plan | Price (USD) | Price (NGN) | vCPU | Memory | Storage | Bandwidth |
|------|-------------|-------------|------|---------|---------|-----------|
| Dedicated Small | $25.00 | ₦37,500 | 2 | 4GB | 80GB | 2TB |
| Dedicated Medium | $50.00 | ₦75,000 | 4 | 8GB | 160GB | 4TB |
| Dedicated Large | $100.00 | ₦150,000 | 8 | 16GB | 320GB | 8TB |

### Enterprise Hosting Plans
| Plan | Price (USD) | Price (NGN) | vCPU | Memory | Storage | Bandwidth |
|------|-------------|-------------|------|---------|---------|-----------|
| Enterprise Standard | $200.00 | ₦300,000 | 16 | 32GB | 640GB | 16TB |
| Enterprise Premium | $400.00 | ₦600,000 | 32 | 64GB | 1280GB | 32TB |

## 🌍 African Market Focus

### Supported Countries & Currencies
- **Nigeria**: Naira (NGN)
- **Kenya**: Kenyan Shilling (KES)
- **South Africa**: Rand (ZAR)
- **Ghana**: Cedi (GHS)
- **Egypt**: Egyptian Pound (EGP)
- **Morocco**: Dirham (MAD)

### Competitive Advantage
- **80-95% cheaper** than international providers
- **Local payment methods** (Mobile money, bank transfer, crypto)
- **African timezone support**
- **Local language support**

## 💰 Profit Analysis

### Example: vhf-1c-1gb Server (100 users)
- **Monthly Revenue**: $403.20
- **Monthly Cost**: $6.00
- **Monthly Profit**: $397.20
- **Profit Margin**: 98.5%
- **Break-even**: 2 users

### Scaling Projections
| Users | Servers | Revenue | Costs | Profit | Margin |
|-------|---------|---------|-------|--------|--------|
| 100 | 1 | $403 | $6 | $397 | 98.5% |
| 300 | 3 | $1,210 | $17 | $1,193 | 98.6% |
| 600 | 6 | $2,419 | $33 | $2,386 | 98.6% |
| 1000 | 10 | $4,032 | $55 | $3,977 | 98.6% |

## 🔧 API Endpoints

### Server Management
```bash
# List available Vultr servers
GET /api/v1/hosting/servers/vultr

# Import a server
POST /api/v1/hosting/servers/import/{instance_id}
{
  "pool_type": "SharedHot"
}

# Get setup instructions
GET /api/v1/hosting/servers/setup/{instance_id}?pool_type=shared-hot
```

### Hosting Plans
```bash
# Get all hosting plans
GET /api/v1/hosting/plans

# Get specific plan
GET /api/v1/hosting/plans/{plan_name}

# Get African pricing
GET /api/v1/hosting/pricing/{country_code}
```

### User Deployment
```bash
# Deploy a user
POST /api/v1/hosting/deploy/user/{user_id}
{
  "plan": "nano",
  "region": "ewr"
}
```

### Analytics & Monitoring
```bash
# Infrastructure status
GET /api/v1/hosting/infrastructure/status

# Profit analysis
POST /api/v1/hosting/analytics/profit
{
  "user_distribution": {
    "nano": 30,
    "micro": 20,
    "small": 20,
    "business": 5
  }
}

# Scaling recommendations
GET /api/v1/hosting/scaling/recommendations
```

## 🛠 Technical Architecture

### Dynamic Resource Sharing
- **CPU Shares**: Weighted fair scheduling using Linux CFS
- **Memory**: Soft limits with burst capability
- **Performance**: 10-50x faster during low traffic
- **Fairness**: Automatic load balancing

### Auto-Scaling
- **Scale Up**: CPU >80% for 5min OR Memory >85% for 3min
- **Scale Down**: CPU <30% for 15min AND Memory <40% for 15min
- **New Server**: When current server hits 90 users

### Risk Mitigation
- **Noisy Neighbor Protection**: Automatic container isolation
- **Resource Contention Detection**: Real-time monitoring
- **Cascade Failure Prevention**: Circuit breakers
- **Emergency Scaling**: Rapid server provisioning

## 📈 Next Steps

1. **Import your first server** using the deployment script
2. **Complete server setup** with the generated script
3. **Deploy test users** to validate the platform
4. **Monitor performance** and scaling recommendations
5. **Scale horizontally** by importing additional servers

## 🎯 Success Metrics

- **Break-even**: 2 users per server
- **Target utilization**: 70-80%
- **Profit margins**: 95-98%
- **Response times**: Sub-100ms for most workloads
- **Uptime**: 99.9%+ with proper monitoring

---

**Ready to revolutionize African hosting? Start importing your servers now!** 🚀🌍
