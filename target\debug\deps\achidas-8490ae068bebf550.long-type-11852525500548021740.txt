fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::http::HeaderMap) -> impl futures::Future<Output = Result<Sse<impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>>, ControllerError>> {stream_build_logs}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::http::HeaderMap) -> impl futures::Future<Output = Result<Sse<impl futures::Stream<Item = Result<axum::response::sse::Event, Infallible>>>, ControllerError>> {stream_build_logs}: Handler<_, _>
