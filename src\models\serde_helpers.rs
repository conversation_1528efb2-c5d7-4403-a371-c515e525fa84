use chrono::{DateTime, Utc};
use serde::{Deserialize, Deserializer, Serialize, Serializer};

// Custom serialization for DateTime<Utc> to work with MongoDB
pub mod datetime_as_bson_datetime {
    use super::*;
    use bson::DateTime as BsonDateTime;

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let bson_datetime = BsonDateTime::from_chrono(*date);
        bson_datetime.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let bson_datetime = BsonDateTime::deserialize(deserializer)?;
        Ok(bson_datetime.to_chrono())
    }
}

// Custom serialization for Option<DateTime<Utc>>
pub mod optional_datetime_as_bson_datetime {
    use super::*;
    use bson::DateTime as BsonDateTime;

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => {
                let bson_datetime = BsonDateTime::from_chrono(*dt);
                bson_datetime.serialize(serializer)
            }
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt: Option<BsonDateTime> = Option::deserialize(deserializer)?;
        Ok(opt.map(|bson_dt| bson_dt.to_chrono()))
    }
}
