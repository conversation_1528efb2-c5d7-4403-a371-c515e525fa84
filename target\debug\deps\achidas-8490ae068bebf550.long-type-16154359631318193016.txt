fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<EnvironmentGroupResponse>>>, ControllerError>> {list_environment_groups}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<EnvironmentGroupResponse>>>, ControllerError>> {list_environment_groups}: Handler<_, _>
