use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;
use super::serde_helpers::datetime_as_bson_datetime;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Instance {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub vultr_instance_id: String,
    pub name: String,
    pub region: String,
    pub plan: String,
    pub os: String,
    pub status: InstanceStatus,
    pub ip_address: Option<String>,
    pub internal_ip: Option<String>,
    pub vcpu_count: u32,
    pub ram: u32, // MB
    pub disk: u32, // GB
    pub bandwidth: u32, // GB
    pub monthly_cost: f64,
    #[serde(with = "datetime_as_bson_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub updated_at: DateTime<Utc>,
    pub tags: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum InstanceStatus {
    Pending,
    Installing,
    Running,
    Stopped,
    Suspended,
    Destroyed,
    Error,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateInstanceRequest {
    pub name: Option<String>,
    pub region: Option<String>,
    pub plan: Option<String>,
    pub os: Option<String>,
    pub ssh_key_ids: Option<Vec<String>>,
    pub startup_script_id: Option<String>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceResponse {
    pub id: String,
    pub vultr_instance_id: String,
    pub name: String,
    pub region: String,
    pub plan: String,
    pub os: String,
    pub status: InstanceStatus,
    pub ip_address: Option<String>,
    pub internal_ip: Option<String>,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    #[serde(with = "datetime_as_bson_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_as_bson_datetime")]
    pub updated_at: DateTime<Utc>,
    pub tags: Vec<String>,
}

impl From<Instance> for InstanceResponse {
    fn from(instance: Instance) -> Self {
        Self {
            id: instance.id.map(|id| id.to_hex()).unwrap_or_default(),
            vultr_instance_id: instance.vultr_instance_id,
            name: instance.name,
            region: instance.region,
            plan: instance.plan,
            os: instance.os,
            status: instance.status,
            ip_address: instance.ip_address,
            internal_ip: instance.internal_ip,
            vcpu_count: instance.vcpu_count,
            ram: instance.ram,
            disk: instance.disk,
            bandwidth: instance.bandwidth,
            monthly_cost: instance.monthly_cost,
            created_at: instance.created_at,
            updated_at: instance.updated_at,
            tags: instance.tags,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlan {
    pub id: String,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    pub type_: String,
    pub locations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegion {
    pub id: String,
    pub city: String,
    pub country: String,
    pub continent: String,
    pub options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOS {
    pub id: String,
    pub name: String,
    pub arch: String,
    pub family: String,
}

// Bare Metal models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetal {
    pub id: String,
    pub os: String,
    pub ram: String,
    pub disk: String,
    pub main_ip: Option<String>,
    pub cpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub tag: Option<String>,
    pub mac_address: Option<String>,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub features: Vec<String>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBareMetalRequest {
    pub region: Option<String>,
    pub plan: Option<String>,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub script_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub ssh_key_ids: Option<Vec<String>>,
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub reserved_ipv4: Option<String>,
    pub activation_email: Option<bool>,
    pub hostname_prefix: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateBareMetalRequest {
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub tag: Option<String>,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv4Info {
    pub ip: String,
    pub netmask: String,
    pub gateway: String,
    pub type_: String,
    pub reverse: String,
    pub mac_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv6Info {
    pub ip: String,
    pub network: String,
    pub network_size: u32,
    pub type_: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUserData {
    pub data: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUpgrades {
    pub os: Vec<u32>,
    pub applications: Vec<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVncInfo {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVpcInfo {
    pub id: String,
    pub mac_address: String,
    pub ip_address: String,
}
