fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {delete_application}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {delete_application}: Handler<_, _>
