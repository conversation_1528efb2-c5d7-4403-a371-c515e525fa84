use crate::{
    config::Config,
    services::{ServiceError, ServiceResult},
    vultr::{VultrClient, CreateInstanceRequest},
};
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, instrument, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KubernetesNodeRequest {
    pub node_type: String,
    pub pool: String,
    pub region: String,
    pub labels: HashMap<String, String>,
    pub taints: Option<Vec<NodeTaint>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeTaint {
    pub key: String,
    pub value: String,
    pub effect: String, // NoSchedule, PreferNoSchedule, NoExecute
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KubernetesNode {
    pub instance_id: String,
    pub node_name: String,
    pub node_type: String,
    pub pool: String,
    pub status: String,
    pub ip_address: String,
    pub labels: HashMap<String, String>,
    pub capacity: NodeCapacity,
    pub cost: NodeCost,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeCapacity {
    pub cpu: String,
    pub memory: String,
    pub storage: String,
    pub max_pods: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeCost {
    pub hourly: f64,
    pub monthly: f64,
    pub yearly: f64,
}

#[derive(Clone)]
pub struct KubernetesDeploymentService {
    vultr_client: VultrClient,
    config: Config,
}

impl KubernetesDeploymentService {
    pub fn new(vultr_client: VultrClient, config: Config) -> Self {
        Self {
            vultr_client,
            config,
        }
    }

    #[instrument(skip(self))]
    pub async fn create_hot_pool_node(&self, region: &str) -> ServiceResult<KubernetesNode> {
        info!("Creating hot pool node with vhf-1c-1gb instance");

        let user_data = self.generate_kubernetes_user_data("hot").await?;
        
        let request = CreateInstanceRequest {
            region: region.to_string(),
            plan: "vhf-1c-1gb".to_string(),
            os_id: Some(387), // Ubuntu 22.04 LTS
            label: Some("achidas-k8s-hot-node".to_string()),
            tag: Some("kubernetes,hot-pool,production".to_string()),
            hostname: Some(format!("k8s-hot-{}", chrono::Utc::now().timestamp())),
            enable_ipv6: Some(true),
            enable_private_network: Some(true),
            user_data: Some(user_data),
            ..Default::default()
        };

        let instance = self
            .vultr_client
            .create_instance_detailed(request)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to create instance: {}", e)))?;

        let mut labels = HashMap::new();
        labels.insert("pool".to_string(), "hot".to_string());
        labels.insert("storage".to_string(), "nvme".to_string());
        labels.insert("priority".to_string(), "high".to_string());
        labels.insert("node-type".to_string(), "vhf-1c-1gb".to_string());

        let node = KubernetesNode {
            instance_id: instance.id.clone(),
            node_name: instance.hostname.clone().unwrap_or_else(|| instance.id.clone()),
            node_type: "vhf-1c-1gb".to_string(),
            pool: "hot".to_string(),
            status: "provisioning".to_string(),
            ip_address: instance.main_ip.clone().unwrap_or_default(),
            labels,
            capacity: NodeCapacity {
                cpu: "900m".to_string(),
                memory: "800Mi".to_string(),
                storage: "28Gi".to_string(),
                max_pods: 20,
            },
            cost: NodeCost {
                hourly: 0.008,
                monthly: 6.00,
                yearly: 72.00,
            },
        };

        info!("Hot pool node created successfully: {}", instance.id);
        Ok(node)
    }

    #[instrument(skip(self))]
    pub async fn create_cold_pool_node(&self, region: &str) -> ServiceResult<KubernetesNode> {
        info!("Creating cold pool node with vc2-1c-1gb instance");

        let user_data = self.generate_kubernetes_user_data("cold").await?;
        
        let request = CreateInstanceRequest {
            region: region.to_string(),
            plan: "vc2-1c-1gb".to_string(),
            os_id: Some(387), // Ubuntu 22.04 LTS
            label: Some("achidas-k8s-cold-node".to_string()),
            tag: Some("kubernetes,cold-pool,production".to_string()),
            hostname: Some(format!("k8s-cold-{}", chrono::Utc::now().timestamp())),
            enable_ipv6: Some(true),
            enable_private_network: Some(true),
            user_data: Some(user_data),
            ..Default::default()
        };

        let instance = self
            .vultr_client
            .create_instance_detailed(request)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to create instance: {}", e)))?;

        let mut labels = HashMap::new();
        labels.insert("pool".to_string(), "cold".to_string());
        labels.insert("storage".to_string(), "ssd".to_string());
        labels.insert("priority".to_string(), "low".to_string());
        labels.insert("node-type".to_string(), "vc2-1c-1gb".to_string());

        let node = KubernetesNode {
            instance_id: instance.id.clone(),
            node_name: instance.hostname.clone().unwrap_or_else(|| instance.id.clone()),
            node_type: "vc2-1c-1gb".to_string(),
            pool: "cold".to_string(),
            status: "provisioning".to_string(),
            ip_address: instance.main_ip.clone().unwrap_or_default(),
            labels,
            capacity: NodeCapacity {
                cpu: "900m".to_string(),
                memory: "800Mi".to_string(),
                storage: "22Gi".to_string(),
                max_pods: 50,
            },
            cost: NodeCost {
                hourly: 0.007,
                monthly: 5.00,
                yearly: 60.00,
            },
        };

        info!("Cold pool node created successfully: {}", instance.id);
        Ok(node)
    }

    #[instrument(skip(self))]
    async fn generate_kubernetes_user_data(&self, pool_type: &str) -> ServiceResult<String> {
        let user_data = format!(
            r#"#!/bin/bash
set -e

# Update system
apt-get update
apt-get upgrade -y

# Install Docker
apt-get install -y docker.io
systemctl enable docker
systemctl start docker

# Install Kubernetes components
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" > /etc/apt/sources.list.d/kubernetes.list
apt-get update
apt-get install -y kubelet kubeadm kubectl
apt-mark hold kubelet kubeadm kubectl

# Configure kubelet
cat > /etc/default/kubelet << EOF
KUBELET_EXTRA_ARGS=--node-labels=pool={},storage={},priority={}
EOF

# Enable kubelet
systemctl enable kubelet

# Install containerd
apt-get install -y containerd
mkdir -p /etc/containerd
containerd config default > /etc/containerd/config.toml
systemctl restart containerd
systemctl enable containerd

# Configure firewall
ufw allow 22/tcp
ufw allow 6443/tcp
ufw allow 10250/tcp
ufw allow 30000:32767/tcp
ufw --force enable

# Create join script placeholder
mkdir -p /opt/kubernetes
cat > /opt/kubernetes/join-cluster.sh << 'EOF'
#!/bin/bash
# This script will be updated with the actual join command
echo "Waiting for cluster join command..."
EOF
chmod +x /opt/kubernetes/join-cluster.sh

# Log completion
echo "Kubernetes node setup completed for {} pool" > /var/log/k8s-setup.log
"#,
            pool_type,
            if pool_type == "hot" { "nvme" } else { "ssd" },
            if pool_type == "hot" { "high" } else { "low" },
            pool_type
        );

        Ok(user_data)
    }

    #[instrument(skip(self))]
    pub async fn get_node_status(&self, instance_id: &str) -> ServiceResult<String> {
        let instance = self
            .vultr_client
            .get_instance_detailed(instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to get instance: {}", e)))?;

        Ok(instance.status)
    }

    #[instrument(skip(self))]
    pub async fn calculate_cluster_capacity(&self, nodes: &[KubernetesNode]) -> ServiceResult<ClusterCapacity> {
        let mut total_cpu = 0.0;
        let mut total_memory = 0.0;
        let mut total_storage = 0.0;
        let mut total_pods = 0;
        let mut total_cost = 0.0;

        for node in nodes {
            // Parse CPU (assuming format like "900m")
            if let Some(cpu_str) = node.capacity.cpu.strip_suffix('m') {
                if let Ok(cpu) = cpu_str.parse::<f64>() {
                    total_cpu += cpu / 1000.0; // Convert millicores to cores
                }
            }

            // Parse memory (assuming format like "800Mi")
            if let Some(mem_str) = node.capacity.memory.strip_suffix("Mi") {
                if let Ok(memory) = mem_str.parse::<f64>() {
                    total_memory += memory / 1024.0; // Convert Mi to Gi
                }
            }

            // Parse storage (assuming format like "28Gi")
            if let Some(storage_str) = node.capacity.storage.strip_suffix("Gi") {
                if let Ok(storage) = storage_str.parse::<f64>() {
                    total_storage += storage;
                }
            }

            total_pods += node.capacity.max_pods;
            total_cost += node.cost.monthly;
        }

        Ok(ClusterCapacity {
            total_nodes: nodes.len() as u32,
            total_cpu_cores: total_cpu,
            total_memory_gb: total_memory,
            total_storage_gb: total_storage,
            total_max_pods: total_pods,
            monthly_cost: total_cost,
            hot_pool_nodes: nodes.iter().filter(|n| n.pool == "hot").count() as u32,
            cold_pool_nodes: nodes.iter().filter(|n| n.pool == "cold").count() as u32,
        })
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusterCapacity {
    pub total_nodes: u32,
    pub total_cpu_cores: f64,
    pub total_memory_gb: f64,
    pub total_storage_gb: f64,
    pub total_max_pods: u32,
    pub monthly_cost: f64,
    pub hot_pool_nodes: u32,
    pub cold_pool_nodes: u32,
}
