use crate::{
    controllers,
    middleware::{
        auth::auth_middleware,
        json_error_handling_middleware,
        handle_404,
        request_id_middleware,
        timing_middleware
    },
    AppState
};
use axum::{
    middleware,
    routing::{delete, get, patch, post, put},
    Router,
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::trace::TraceLayer;

pub fn create_router(state: Arc<AppState>) -> Router {
    Router::new()
        // Health check (public)
        .route("/health", get(controllers::health::health_check))
        .route("/api/v1/health", get(controllers::health::health_check))

        // Authentication routes (public) - versioned
        .route("/api/v1/auth/register", post(controllers::auth::register))
        .route("/api/v1/auth/login", post(controllers::auth::login))
        .route("/api/v1/auth/refresh", post(controllers::auth::refresh_token))

        // Webhook routes (public) - versioned
        .route("/api/v1/webhooks/github", post(controllers::webhooks::handle_github_webhook))
        .route("/api/v1/webhooks/gitlab", post(controllers::webhooks::handle_gitlab_webhook))
        .route("/api/v1/webhooks/git/:app_id", post(controllers::webhooks::handle_application_webhook))
        .route("/api/v1/webhooks/health", get(controllers::webhooks::webhook_health_check))
        .route("/api/v1/webhooks", post(controllers::webhooks::handle_generic_webhook))

        // Protected routes
        .nest("/api/v1", protected_routes().layer(middleware::from_fn_with_state(state.clone(), auth_middleware)))

        // Fallback handler for 404s
        .fallback(handle_404)

        // Add state and middleware layers
        .with_state(state)
        .layer(ServiceBuilder::new()
            .layer(middleware::from_fn(request_id_middleware))
            .layer(middleware::from_fn(timing_middleware))
            .layer(middleware::from_fn(json_error_handling_middleware))
            .layer(TraceLayer::new_for_http())
        )
}

fn protected_routes() -> Router<Arc<AppState>> {
    Router::new()
        // User management
        .route("/users/profile", get(controllers::users::get_profile))
        // .route("/users/profile", put(controllers::users::update_profile))

        // Platform Services - Applications
        .route("/applications", get(controllers::applications::list_applications))
        .route("/applications", post(controllers::applications::create_application))
        .route("/applications/:app_id", get(controllers::applications::get_application))
        .route("/applications/:app_id", put(controllers::applications::update_application))
        .route("/applications/:app_id", delete(controllers::applications::delete_application))
        .route("/applications/:app_id/deploy", post(controllers::applications::trigger_deployment))
        .route("/applications/:app_id/deployments", get(controllers::applications::list_deployments))
        .route("/applications/:app_id/deployments/:deployment_id", get(controllers::applications::get_deployment))
        .route("/applications/:app_id/deployments/:deployment_id/rollback", post(controllers::applications::rollback_deployment))

        // Platform Services - Environment & Secrets
        .route("/environment-groups", get(controllers::environment::list_environment_groups))
        .route("/environment-groups", post(controllers::environment::create_environment_group))
        .route("/environment-groups/:group_id", get(controllers::environment::get_environment_group))
        .route("/environment-groups/:group_id/variables", post(controllers::environment::set_environment_variable))
        .route("/environment-groups/:group_id/variables/:key", delete(controllers::environment::delete_environment_variable))
        .route("/environment-groups/:group_id/secrets", post(controllers::environment::set_secret))
        .route("/environment-groups/:group_id/secrets/:key", delete(controllers::environment::delete_secret))
        .route("/environment-groups/:group_id/link", post(controllers::environment::link_application))

        // Platform Services - Logs & Monitoring
        .route("/applications/:app_id/logs", get(controllers::logs::get_application_logs))
        .route("/applications/:app_id/logs/stream", get(controllers::logs::stream_application_logs))
        .route("/applications/:app_id/deployments/:deployment_id/logs", get(controllers::logs::get_deployment_logs))
        .route("/builds/:job_id/logs", get(controllers::logs::get_build_logs))
        .route("/builds/:job_id/logs/stream", get(controllers::logs::stream_build_logs))

        // Instance management
        .route("/instances", get(controllers::instances::list_instances))
        // .route("/instances", post(controllers::instances::create_instance))
        .route("/instances/:id", get(controllers::instances::get_instance))
        // .route("/instances/:id", put(controllers::instances::update_instance))
        .route("/instances/:id", delete(controllers::instances::delete_instance))
        .route("/instances/:id/start", post(controllers::instances::start_instance))
        .route("/instances/:id/stop", post(controllers::instances::stop_instance))
        .route("/instances/:id/restart", post(controllers::instances::restart_instance))
        
        // Cloud Infrastructure
        .route("/account", get(controllers::vultr::get_account))
        .route("/account/usage", get(controllers::vultr::get_account_bandwidth))
        .route("/plans", get(controllers::vultr::list_plans))
        .route("/regions", get(controllers::vultr::list_regions))
        .route("/operating-systems", get(controllers::vultr::list_os))
        .route("/ssh-keys", get(controllers::vultr::list_ssh_keys))
        .route("/ssh-keys", post(controllers::vultr::create_ssh_key))
        .route("/ssh-keys/:id", get(controllers::vultr::get_ssh_key))
        .route("/ssh-keys/:id", patch(controllers::vultr::update_ssh_key))
        .route("/ssh-keys/:id", delete(controllers::vultr::delete_ssh_key))
        .route("/backups", get(controllers::vultr::list_backups))

        // Dedicated Servers
        .route("/servers", get(controllers::vultr::list_bare_metal))
        .route("/servers", post(controllers::vultr::create_bare_metal))
        .route("/servers/:id", get(controllers::vultr::get_bare_metal))
        .route("/servers/:id", put(controllers::vultr::update_bare_metal))
        .route("/servers/:id", delete(controllers::vultr::delete_bare_metal))
        .route("/servers/bare-metal/:id/ipv4", get(controllers::vultr::get_bare_metal_ipv4))
        .route("/servers/bare-metal/:id/ipv6", get(controllers::vultr::get_bare_metal_ipv6))
        .route("/servers/bare-metal/:id/bandwidth", get(controllers::vultr::get_bare_metal_bandwidth))
        .route("/servers/bare-metal/:id/user-data", get(controllers::vultr::get_bare_metal_user_data))
        .route("/servers/bare-metal/:id/upgrades", get(controllers::vultr::get_bare_metal_upgrades))
        .route("/servers/bare-metal/:id/vnc", get(controllers::vultr::get_bare_metal_vnc))
        // Bare Metal Operations
        .route("/servers/bare-metal/:id/start", post(controllers::vultr::start_bare_metal))
        .route("/servers/bare-metal/:id/reboot", post(controllers::vultr::reboot_bare_metal))
        .route("/servers/bare-metal/:id/reinstall", post(controllers::vultr::reinstall_bare_metal))
        .route("/servers/bare-metal/:id/halt", post(controllers::vultr::halt_bare_metal))
        .route("/servers/bare-metal/halt", post(controllers::vultr::halt_bare_metals))
        .route("/servers/bare-metal/reboot", post(controllers::vultr::reboot_bare_metals))
        .route("/servers/bare-metal/start", post(controllers::vultr::start_bare_metals))
        .route("/servers/bare-metal/:id/vpcs", get(controllers::vultr::list_bare_metal_vpcs))
        .route("/servers/bare-metal/:id/vpcs/attach", post(controllers::vultr::attach_bare_metal_vpc))
        .route("/servers/bare-metal/:id/vpcs/detach", post(controllers::vultr::detach_bare_metal_vpc))
        
        // Block Storage
        .route("/storage/blocks", get(controllers::vultr::list_block_storage))
        .route("/storage/blocks", post(controllers::vultr::create_block_storage))
        .route("/storage/blocks/:id", get(controllers::vultr::get_block_storage))
        .route("/storage/blocks/:id", put(controllers::vultr::update_block_storage))
        .route("/storage/blocks/:id", delete(controllers::vultr::delete_block_storage))
        .route("/storage/blocks/:id/attach", post(controllers::vultr::attach_block_storage))
        .route("/storage/blocks/:id/detach", post(controllers::vultr::detach_block_storage))

        // Additional Billing routes
        .route("/billing/invoice-items/:invoice_id", get(controllers::vultr::get_invoice_items))
        .route("/billing/pending-charges", get(controllers::vultr::get_pending_charges))

        // CDN Pull Zones
        .route("/cdn/pull-zones", get(controllers::vultr::list_pull_zones))
        .route("/cdn/pull-zones", post(controllers::vultr::create_pull_zone))
        .route("/cdn/pull-zones/:id", get(controllers::vultr::get_pull_zone))
        .route("/cdn/pull-zones/:id", put(controllers::vultr::update_pull_zone))
        .route("/cdn/pull-zones/:id", delete(controllers::vultr::delete_pull_zone))
        .route("/cdn/pull-zones/:id/purge", post(controllers::vultr::purge_pull_zone))

        // CDN Push Zones
        .route("/cdn/push-zones", get(controllers::vultr::list_push_zones))
        .route("/cdn/push-zones", post(controllers::vultr::create_push_zone))
        .route("/cdn/push-zones/:id", get(controllers::vultr::get_push_zone))
        .route("/cdn/push-zones/:id", put(controllers::vultr::update_push_zone))
        .route("/cdn/push-zones/:id", delete(controllers::vultr::delete_push_zone))
        .route("/cdn/push-zones/:id/files", get(controllers::vultr::get_push_zone_files))
        .route("/cdn/push-zones/:id/files/:file_name", delete(controllers::vultr::delete_push_zone_file))

        // Container Registry routes
        .route("/registry", post(controllers::vultr::create_registry))
        .route("/registry/:id", put(controllers::vultr::update_registry))
        .route("/registry/:id", delete(controllers::vultr::delete_registry))
        .route("/registry/:id/replication", get(controllers::vultr::list_registry_replications))
        .route("/registry/:id/replication", post(controllers::vultr::create_registry_replication))
        .route("/registry/:id/replication/:replication_id", get(controllers::vultr::get_registry_replication))
        .route("/registry/:id/replication/:replication_id", delete(controllers::vultr::delete_registry_replication))
        .route("/registry/:id/repository", get(controllers::vultr::list_registry_repositories))
        .route("/registry/:id/repository/:repository_image", get(controllers::vultr::get_registry_repository))
        .route("/registry/:id/repository/:repository_image", put(controllers::vultr::update_registry_repository))
        .route("/registry/:id/repository/:repository_image", delete(controllers::vultr::delete_registry_repository))
        .route("/registry/:id/docker-credentials", post(controllers::vultr::create_registry_docker_credentials))
        .route("/registry/:id/kubernetes-docker-credentials", post(controllers::vultr::create_registry_kubernetes_docker_credentials))
        .route("/registry/:id/password", put(controllers::vultr::update_registry_password))
        .route("/registry/:id/robot", get(controllers::vultr::list_registry_robots))
        .route("/registry/:id/robot/:robot_name", get(controllers::vultr::get_registry_robot))
        .route("/registry/:id/robot/:robot_name", put(controllers::vultr::update_registry_robot))
        .route("/registry/:id/robot/:robot_name", delete(controllers::vultr::delete_registry_robot))
        .route("/registry/:id/repository/:repository_image/artifact", get(controllers::vultr::list_registry_repository_artifacts))
        .route("/registry/:id/repository/:repository_image/artifact/:artifact_digest", get(controllers::vultr::get_registry_repository_artifact))
        .route("/registry/:id/repository/:repository_image/artifact/:artifact_digest", delete(controllers::vultr::delete_registry_repository_artifact))
        .route("/registry/regions", get(controllers::vultr::list_registry_regions))
        .route("/registry/plans", get(controllers::vultr::list_registry_plans))

        // DNS routes
        .route("/dns/domains", get(controllers::vultr::list_dns_domains))
        .route("/dns/domains", post(controllers::vultr::create_dns_domain))
        .route("/dns/domains/:domain", get(controllers::vultr::get_dns_domain))
        .route("/dns/domains/:domain", put(controllers::vultr::update_dns_domain))
        .route("/dns/domains/:domain", delete(controllers::vultr::delete_dns_domain))
        .route("/dns/domains/:domain/soa", get(controllers::vultr::get_dns_domain_soa))
        .route("/dns/domains/:domain/soa", patch(controllers::vultr::update_dns_domain_soa))
        .route("/dns/domains/:domain/dnssec", get(controllers::vultr::get_dns_domain_dnssec))
        .route("/dns/domains/:domain/records", get(controllers::vultr::list_dns_domain_records))
        .route("/dns/domains/:domain/records", post(controllers::vultr::create_dns_domain_record))
        .route("/dns/domains/:domain/records/:record_id", get(controllers::vultr::get_dns_domain_record))
        .route("/dns/domains/:domain/records/:record_id", patch(controllers::vultr::update_dns_domain_record))
        .route("/dns/domains/:domain/records/:record_id", delete(controllers::vultr::delete_dns_domain_record))

        // Firewall routes
        .route("/firewalls", get(controllers::vultr::list_firewall_groups))
        .route("/firewalls", post(controllers::vultr::create_firewall_group))
        .route("/firewalls/:firewall_group_id", get(controllers::vultr::get_firewall_group))
        .route("/firewalls/:firewall_group_id", put(controllers::vultr::update_firewall_group))
        .route("/firewalls/:firewall_group_id", delete(controllers::vultr::delete_firewall_group))
        .route("/firewalls/:firewall_group_id/rules", get(controllers::vultr::list_firewall_group_rules))
        .route("/firewalls/:firewall_group_id/rules", post(controllers::vultr::create_firewall_group_rule))
        .route("/firewalls/:firewall_group_id/rules/:firewall_rule_id", get(controllers::vultr::get_firewall_group_rule))
        .route("/firewalls/:firewall_group_id/rules/:firewall_rule_id", delete(controllers::vultr::delete_firewall_group_rule))

        // Cloud Instance routes (detailed)
        .route("/cloud/instances", get(controllers::vultr::list_instances_detailed))
        .route("/cloud/instances", post(controllers::vultr::create_instance_detailed))
        .route("/cloud/instances/:instance_id", get(controllers::vultr::get_instance_detailed))
        .route("/cloud/instances/:instance_id", patch(controllers::vultr::update_instance_detailed))
        .route("/cloud/instances/:instance_id", delete(controllers::vultr::delete_instance_detailed))
        .route("/cloud/instances/halt", post(controllers::vultr::halt_instances))
        .route("/cloud/instances/reboot", post(controllers::vultr::reboot_instances))
        .route("/cloud/instances/start", post(controllers::vultr::start_instances))
        .route("/cloud/instances/:instance_id/halt", post(controllers::vultr::halt_instance))
        .route("/cloud/instances/:instance_id/reboot", post(controllers::vultr::reboot_instance))
        .route("/cloud/instances/:instance_id/start", post(controllers::vultr::start_instance))
        .route("/cloud/instances/:instance_id/reinstall", post(controllers::vultr::reinstall_instance))
        .route("/cloud/instances/:instance_id/bandwidth", get(controllers::vultr::get_instance_bandwidth))
        .route("/cloud/instances/:instance_id/neighbors", get(controllers::vultr::get_instance_neighbors))
        .route("/cloud/instances/:instance_id/vpcs", get(controllers::vultr::list_instance_vpcs))
        .route("/cloud/instances/:instance_id/iso", get(controllers::vultr::get_instance_iso_status))
        .route("/cloud/instances/:instance_id/iso/attach", post(controllers::vultr::attach_instance_iso))
        .route("/cloud/instances/:instance_id/iso/detach", post(controllers::vultr::detach_instance_iso))
        .route("/cloud/instances/:instance_id/vpcs/attach", post(controllers::vultr::attach_instance_vpc))
        .route("/cloud/instances/:instance_id/vpcs/detach", post(controllers::vultr::detach_instance_vpc))
        .route("/cloud/instances/:instance_id/backup-schedule", post(controllers::vultr::create_instance_backup_schedule))
        .route("/cloud/instances/:instance_id/backup-schedule", get(controllers::vultr::get_instance_backup_schedule))
        .route("/cloud/instances/:instance_id/restore", post(controllers::vultr::restore_instance))
        .route("/cloud/instances/:instance_id/ipv4", get(controllers::vultr::get_instance_ipv4))
        .route("/cloud/instances/:instance_id/ipv4", post(controllers::vultr::create_instance_ipv4))
        .route("/cloud/instances/:instance_id/ipv4/:ipv4", delete(controllers::vultr::delete_instance_ipv4))
        .route("/cloud/instances/:instance_id/ipv6", get(controllers::vultr::get_instance_ipv6))
        .route("/cloud/instances/:instance_id/ipv6/reverse", post(controllers::vultr::create_instance_reverse_ipv6))
        .route("/cloud/instances/:instance_id/ipv6/reverse", get(controllers::vultr::list_instance_ipv6_reverse))
        .route("/cloud/instances/:instance_id/ipv6/reverse/:ipv6", delete(controllers::vultr::delete_instance_reverse_ipv6))
        .route("/cloud/instances/:instance_id/ipv4/reverse", post(controllers::vultr::create_instance_reverse_ipv4))
        .route("/cloud/instances/:instance_id/ipv4/reverse/default/:ip", post(controllers::vultr::set_instance_default_reverse_ipv4))
        .route("/cloud/instances/:instance_id/user-data", get(controllers::vultr::get_instance_userdata))
        .route("/cloud/instances/:instance_id/upgrades", get(controllers::vultr::get_instance_upgrades))
        .route("/cloud/instances/:instance_id/jobs/:job_id", get(controllers::vultr::get_instance_job))

        // ISO routes
        .route("/vultr/iso", get(controllers::vultr::list_isos))
        .route("/vultr/iso", post(controllers::vultr::create_iso))
        .route("/vultr/iso/:iso_id", get(controllers::vultr::get_iso))
        .route("/vultr/iso/:iso_id", delete(controllers::vultr::delete_iso))
        .route("/vultr/iso-public", get(controllers::vultr::list_public_isos))

        // Enhanced Kubernetes routes
        .route("/vultr/kubernetes/clusters", post(controllers::vultr::create_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters", get(controllers::vultr::list_kubernetes_clusters))
        .route("/vultr/kubernetes/clusters/:vke_id", get(controllers::vultr::get_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id", put(controllers::vultr::update_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id", delete(controllers::vultr::delete_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id/delete-with-linked-resources", delete(controllers::vultr::delete_kubernetes_cluster_with_resources))
        .route("/vultr/kubernetes/clusters/:vke_id/resources", get(controllers::vultr::get_kubernetes_resources))
        .route("/vultr/kubernetes/clusters/:vke_id/available-upgrades", get(controllers::vultr::get_kubernetes_available_upgrades))
        .route("/vultr/kubernetes/clusters/:vke_id/upgrades", post(controllers::vultr::start_kubernetes_cluster_upgrade))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools", post(controllers::vultr::create_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools", get(controllers::vultr::get_nodepools))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", get(controllers::vultr::get_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", patch(controllers::vultr::update_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", delete(controllers::vultr::delete_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id/nodes/:node_id", delete(controllers::vultr::delete_nodepool_instance))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id/nodes/:node_id/recycle", post(controllers::vultr::recycle_nodepool_instance))
        .route("/vultr/kubernetes/clusters/:vke_id/config", get(controllers::vultr::get_kubernetes_cluster_config))
        .route("/vultr/kubernetes/versions", get(controllers::vultr::get_kubernetes_versions))

        // Enhanced Load Balancer routes
        .route("/vultr/load-balancers", get(controllers::vultr::list_load_balancers))
        .route("/vultr/load-balancers", post(controllers::vultr::create_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", get(controllers::vultr::get_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", patch(controllers::vultr::update_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", delete(controllers::vultr::delete_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id/ssl", delete(controllers::vultr::delete_load_balancer_ssl))
        .route("/vultr/load-balancers/:load_balancer_id/ssl/auto", delete(controllers::vultr::delete_load_balancer_auto_ssl))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules", post(controllers::vultr::create_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules/:forwarding_rule_id", get(controllers::vultr::get_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules/:forwarding_rule_id", delete(controllers::vultr::delete_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/firewall-rules", get(controllers::vultr::list_load_balancer_firewall_rules))
        .route("/vultr/load-balancers/:load_balancer_id/firewall-rules/:firewall_rule_id", get(controllers::vultr::get_load_balancer_firewall_rule))

        // Managed Database routes
        .route("/vultr/databases/plans", get(controllers::vultr::list_database_plans))
        .route("/vultr/databases", get(controllers::vultr::list_managed_databases))
        .route("/vultr/databases", post(controllers::vultr::create_managed_database))
        .route("/vultr/databases/:database_id", get(controllers::vultr::get_managed_database))
        .route("/vultr/databases/:database_id", put(controllers::vultr::update_managed_database))
        .route("/vultr/databases/:database_id", delete(controllers::vultr::delete_managed_database))
        .route("/vultr/databases/:database_id/usage", get(controllers::vultr::get_database_usage))
        .route("/vultr/databases/:database_id/users", get(controllers::vultr::list_database_users))
        .route("/vultr/databases/:database_id/users", post(controllers::vultr::create_database_user))
        .route("/vultr/databases/:database_id/users/:username", get(controllers::vultr::get_database_user))
        .route("/vultr/databases/:database_id/users/:username", put(controllers::vultr::update_database_user))
        .route("/vultr/databases/:database_id/users/:username", delete(controllers::vultr::delete_database_user))
        .route("/vultr/databases/:database_id/users/:username/access-control", put(controllers::vultr::set_database_user_acl))

        // Marketplace routes
        .route("/vultr/marketplace/apps/:image_id/variables", get(controllers::vultr::list_marketplace_app_variables))

        // Object Storage (S3) routes
        .route("/vultr/object-storage", get(controllers::vultr::list_object_storages))
        .route("/vultr/object-storage", post(controllers::vultr::create_object_storage))
        .route("/vultr/object-storage/:object_storage_id", get(controllers::vultr::get_object_storage))
        .route("/vultr/object-storage/:object_storage_id", delete(controllers::vultr::delete_object_storage))
        .route("/vultr/object-storage/:object_storage_id", put(controllers::vultr::update_object_storage))
        .route("/vultr/object-storage/:object_storage_id/regenerate-keys", post(controllers::vultr::regenerate_object_storage_keys))
        .route("/vultr/object-storage/clusters", get(controllers::vultr::list_object_storage_clusters))
        .route("/vultr/object-storage/tiers", get(controllers::vultr::list_object_storage_tiers))
        .route("/vultr/object-storage/clusters/:cluster_id/tiers", get(controllers::vultr::list_object_storage_cluster_tiers))

        // Operating System routes
        .route("/vultr/os", get(controllers::vultr::list_os))

        // Plans routes
        .route("/vultr/plans", get(controllers::vultr::list_plans))
        .route("/vultr/plans-metal", get(controllers::vultr::list_metal_plans))

        // Serverless Inference routes
        .route("/vultr/inference", get(controllers::vultr::list_inference))
        .route("/vultr/inference", post(controllers::vultr::create_inference))
        .route("/vultr/inference/:inference_id", get(controllers::vultr::get_inference))
        .route("/vultr/inference/:inference_id", patch(controllers::vultr::update_inference))
        .route("/vultr/inference/:inference_id", delete(controllers::vultr::delete_inference))
        .route("/vultr/inference/:inference_id/usage", get(controllers::vultr::get_inference_usage))

        // VPC routes
        .route("/vultr/vpcs", get(controllers::vultr::list_vpcs))
        .route("/vultr/vpcs", post(controllers::vultr::create_vpc))
        .route("/vultr/vpcs/:vpc_id", get(controllers::vultr::get_vpc))
        .route("/vultr/vpcs/:vpc_id", put(controllers::vultr::update_vpc))
        .route("/vultr/vpcs/:vpc_id", delete(controllers::vultr::delete_vpc))

        // Reserved IP routes
        .route("/vultr/reserved-ips", get(controllers::vultr::list_reserved_ips))
        .route("/vultr/reserved-ips", post(controllers::vultr::create_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", get(controllers::vultr::get_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", patch(controllers::vultr::update_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", delete(controllers::vultr::delete_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip/attach", post(controllers::vultr::attach_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip/detach", post(controllers::vultr::detach_reserved_ip))
        .route("/vultr/reserved-ips/convert", post(controllers::vultr::convert_reserved_ip))

        // Region routes
        .route("/vultr/regions", get(controllers::vultr::list_regions))
        .route("/vultr/regions/:region_id/availability", get(controllers::vultr::list_available_plans_region))

        // Snapshot routes
        .route("/vultr/snapshots", get(controllers::vultr::list_snapshots))
        .route("/vultr/snapshots", post(controllers::vultr::create_snapshot))
        .route("/vultr/snapshots/create-from-url", post(controllers::vultr::create_snapshot_from_url))
        .route("/vultr/snapshots/:snapshot_id", get(controllers::vultr::get_snapshot))
        .route("/vultr/snapshots/:snapshot_id", put(controllers::vultr::update_snapshot))
        .route("/vultr/snapshots/:snapshot_id", delete(controllers::vultr::delete_snapshot))

        // Subaccount routes
        .route("/vultr/subaccounts", get(controllers::vultr::list_subaccounts))
        .route("/vultr/subaccounts", post(controllers::vultr::create_subaccount))

        // SSH Key routes
        .route("/vultr/ssh-keys", get(controllers::vultr::list_ssh_keys))
        .route("/vultr/ssh-keys", post(controllers::vultr::create_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", get(controllers::vultr::get_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", delete(controllers::vultr::delete_ssh_key))

        // Startup Script routes
        .route("/vultr/startup-scripts", get(controllers::vultr::list_startup_scripts))
        .route("/vultr/startup-scripts", post(controllers::vultr::create_startup_script))
        .route("/vultr/startup-scripts/:script_id", get(controllers::vultr::get_startup_script))
        .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
        .route("/vultr/startup-scripts/:script_id", delete(controllers::vultr::delete_startup_script))

        // Storage Gateway routes
        .route("/vultr/storage-gateways", get(controllers::vultr::list_storage_gateways))
        .route("/vultr/storage-gateways", post(controllers::vultr::create_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", get(controllers::vultr::get_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", put(controllers::vultr::update_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", delete(controllers::vultr::delete_storage_gateway))

        // User routes
        .route("/vultr/users", get(controllers::vultr::list_users))
        .route("/vultr/users", post(controllers::vultr::create_user))
        .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
        .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
        .route("/vultr/users/:user_id", delete(controllers::vultr::delete_user))

        // VFS (Vultr File System) routes
        .route("/vultr/vfs/regions", get(controllers::vultr::list_vfs_regions))
        .route("/vultr/vfs", get(controllers::vultr::list_vfs))
        .route("/vultr/vfs", post(controllers::vultr::create_vfs))
        .route("/vultr/vfs/:vfs_id", get(controllers::vultr::get_vfs))
        .route("/vultr/vfs/:vfs_id", put(controllers::vultr::update_vfs))
        .route("/vultr/vfs/:vfs_id", delete(controllers::vultr::delete_vfs))

        // Billing
        .route("/billing", get(controllers::billing::get_billing_info))
        .route("/billing/invoices", get(controllers::billing::list_invoices))
        .route("/billing/invoices/:id", get(controllers::billing::get_invoice))
        .route("/billing/usage", get(controllers::billing::get_usage))

        // Intelligent Hosting Platform
        .route("/hosting/plans", get(controllers::intelligent_hosting::get_hosting_plans))
        .route("/hosting/plans/:plan_name", get(controllers::intelligent_hosting::get_hosting_plan))
        .route("/hosting/servers/vultr", get(controllers::intelligent_hosting::list_vultr_servers))
        .route("/hosting/servers/vultr/:instance_id", get(controllers::intelligent_hosting::get_server_details))
        .route("/hosting/servers/import/:instance_id", post(controllers::intelligent_hosting::import_server))
        .route("/hosting/servers/setup/:instance_id", get(controllers::intelligent_hosting::get_server_setup))
        .route("/hosting/deploy/user/:user_id", post(controllers::intelligent_hosting::deploy_user))
        .route("/hosting/infrastructure/status", get(controllers::intelligent_hosting::get_infrastructure_status))
        .route("/hosting/analytics/profit", post(controllers::intelligent_hosting::calculate_profit_analysis))
        .route("/hosting/scaling/recommendations", get(controllers::intelligent_hosting::get_scaling_recommendations))
        .route("/hosting/scaling/execute", post(controllers::intelligent_hosting::execute_scaling_action))
        .route("/hosting/pricing/:country_code", get(controllers::intelligent_hosting::get_african_pricing))
        .route("/hosting/health", get(controllers::intelligent_hosting::health_check))

        // Admin routes
        .nest("/admin", admin_routes())
        
        // Authentication middleware applied above
}

fn admin_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/users", get(controllers::admin::list_users))
        .route("/users/:id", get(controllers::admin::get_user))
        .route("/users/:id/suspend", post(controllers::admin::suspend_user))
        .route("/users/:id/activate", post(controllers::admin::activate_user))
        .route("/instances", get(controllers::admin::list_all_instances))
        .route("/billing/overview", get(controllers::admin::billing_overview))
        .route("/metrics", get(controllers::admin::get_metrics))
        // Admin authentication middleware would be added here
}
