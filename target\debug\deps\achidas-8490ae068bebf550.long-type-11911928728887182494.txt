fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, Query<models::disk::Pagination>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<models::deployment::ApplicationResponse>>>, ControllerError>> {list_applications}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, Query<models::disk::Pagination>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<models::deployment::ApplicationResponse>>>, ControllerError>> {list_applications}: Handler<_, _>
