fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::J<PERSON><ApiResponse<()>>, ControllerError>> {delete_environment_variable}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::J<PERSON><ApiResponse<()>>, ControllerError>> {delete_environment_variable}: Handler<_, _>
