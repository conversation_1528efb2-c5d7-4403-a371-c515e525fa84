fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, Query<models::disk::Pagination>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<models::deployment::DeploymentResponse>>>, ControllerError>> {list_deployments}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, Query<models::disk::Pagination>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<Vec<models::deployment::DeploymentResponse>>>, ControllerError>> {list_deployments}: Handler<_, _>
