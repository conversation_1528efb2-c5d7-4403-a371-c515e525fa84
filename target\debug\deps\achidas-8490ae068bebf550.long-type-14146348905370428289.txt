fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::Json<models::deployment::CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {create_application}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::Json<models::deployment::CreateApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {create_application}: Handler<_, _>
