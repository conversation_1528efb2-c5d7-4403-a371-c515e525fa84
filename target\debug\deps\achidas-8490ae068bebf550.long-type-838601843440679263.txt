fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<models::deployment::TriggerDeploymentRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::DeploymentResponse>>, ControllerError>> {trigger_deployment}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<models::deployment::TriggerDeploymentRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::DeploymentResponse>>, ControllerError>> {trigger_deployment}: Handler<_, _>
