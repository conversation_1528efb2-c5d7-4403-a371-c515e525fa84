#!/bin/bash

# Test script to get details of your manually created Vultr server
# Usage: ./test_server_details.sh <instance_id>

set -e

# Configuration
API_BASE_URL=${API_BASE_URL:-"http://localhost:3000/api/v1"}
AUTH_TOKEN=${AUTH_TOKEN:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed. Please install curl."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed. Please install jq."
        exit 1
    fi
    
    # Check if AUTH_TOKEN is set
    if [ -z "$AUTH_TOKEN" ]; then
        log_error "AUTH_TOKEN environment variable is required."
        log_info "Please set AUTH_TOKEN with your authentication token."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Check API health
check_api_health() {
    log_info "Checking API health..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$API_BASE_URL/health" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "API is healthy"
    else
        log_error "API health check failed (HTTP $response)"
        if [ -f /tmp/health_response.json ]; then
            cat /tmp/health_response.json
        fi
        exit 1
    fi
}

# List all Vultr servers
list_all_servers() {
    log_info "Fetching all Vultr servers..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/servers_response.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/servers/vultr")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Vultr servers fetched successfully"
        
        # Display available servers
        log_info "Your Vultr servers:"
        echo ""
        printf "%-20s %-15s %-12s %-10s %-15s %-10s %s\n" "Instance ID" "Plan" "Region" "Status" "IP Address" "Cost/Month" "Suitable"
        echo "--------------------------------------------------------------------------------------------------------"
        
        jq -r '.data[] | "\(.instance_id) \(.plan) \(.region) \(.status) \(.main_ip) $\(.monthly_cost) \(.suitable_for_achidas)"' /tmp/servers_response.json | \
        while read -r line; do
            printf "%-20s %-15s %-12s %-10s %-15s %-10s %s\n" $line
        done
        echo ""
        
        # Show recommended servers
        log_info "Recommended servers for Achidas hosting:"
        jq -r '.data[] | select(.suitable_for_achidas == true) | "  - \(.instance_id) (\(.plan)) - \(.recommended_pool) pool - $\(.monthly_cost)/month"' /tmp/servers_response.json
        echo ""
        
    else
        log_error "Failed to fetch Vultr servers (HTTP $http_code)"
        if [ -f /tmp/servers_response.json ]; then
            cat /tmp/servers_response.json
        fi
        exit 1
    fi
}

# Get specific server details
get_server_details() {
    local instance_id=$1
    
    if [ -z "$instance_id" ]; then
        log_error "Instance ID is required"
        log_info "Usage: $0 <instance_id>"
        return 1
    fi
    
    log_info "Getting details for server: $instance_id"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/server_details.json \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$API_BASE_URL/hosting/servers/vultr/$instance_id")
    
    http_code=$(echo "$response" | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        log_success "Server details retrieved successfully"
        
        # Extract and display server details
        instance_id=$(jq -r '.data.instance_id' /tmp/server_details.json)
        label=$(jq -r '.data.label' /tmp/server_details.json)
        plan=$(jq -r '.data.plan' /tmp/server_details.json)
        region=$(jq -r '.data.region' /tmp/server_details.json)
        status=$(jq -r '.data.status' /tmp/server_details.json)
        main_ip=$(jq -r '.data.main_ip' /tmp/server_details.json)
        monthly_cost=$(jq -r '.data.monthly_cost' /tmp/server_details.json)
        recommended_pool=$(jq -r '.data.recommended_pool' /tmp/server_details.json)
        max_users=$(jq -r '.data.max_users' /tmp/server_details.json)
        created_date=$(jq -r '.data.created_date' /tmp/server_details.json)
        suitable=$(jq -r '.data.suitable_for_achidas' /tmp/server_details.json)
        
        echo ""
        log_info "=== SERVER DETAILS ==="
        echo "  Instance ID: $instance_id"
        echo "  Label: $label"
        echo "  Plan: $plan"
        echo "  Region: $region"
        echo "  Status: $status"
        echo "  IP Address: $main_ip"
        echo "  Monthly Cost: \$$monthly_cost"
        echo "  Recommended Pool: $recommended_pool"
        echo "  Max Users: $max_users"
        echo "  Created: $created_date"
        echo "  Suitable for Achidas: $suitable"
        echo ""
        
        if [ "$suitable" = "true" ]; then
            log_success "✅ This server is suitable for Achidas hosting!"
            echo ""
            log_info "Next steps:"
            echo "  1. Import this server: ./deploy_first_server.sh $instance_id ${recommended_pool,,}"
            echo "  2. Or use the API: POST /api/v1/hosting/servers/import/$instance_id"
            echo "  3. Get setup instructions: GET /api/v1/hosting/servers/setup/$instance_id"
        else
            log_warning "⚠️  This server plan may not be optimal for Achidas hosting"
            log_info "Recommended plans: vhf-1c-1gb, vc2-1c-1gb, vc2-2c-4gb, vc2-4c-8gb, vc2-8c-16gb"
        fi
        
    elif [ "$http_code" = "404" ]; then
        log_error "Server not found (HTTP 404)"
        log_info "Please check the instance ID and make sure it exists in your Vultr account"
    else
        log_error "Failed to get server details (HTTP $http_code)"
        if [ -f /tmp/server_details.json ]; then
            cat /tmp/server_details.json
        fi
        exit 1
    fi
}

# Test import functionality
test_import() {
    local instance_id=$1
    local pool_type=${2:-"shared-hot"}
    
    log_info "Testing import functionality for server: $instance_id"
    
    # Convert pool type to API format
    api_pool_type=""
    case $pool_type in
        "shared-hot") api_pool_type="SharedHot" ;;
        "shared-cold") api_pool_type="SharedCold" ;;
        "dedicated") api_pool_type="Dedicated" ;;
        "enterprise") api_pool_type="Enterprise" ;;
        *) log_error "Invalid pool type: $pool_type"; return 1 ;;
    esac
    
    request_body="{\"pool_type\": \"$api_pool_type\"}"
    
    log_info "Import request: $request_body"
    log_warning "Note: This is a test - the server won't actually be imported"
    
    # Just show what the request would look like
    echo ""
    log_info "To import this server, run:"
    echo "curl -X POST \\"
    echo "  -H \"Authorization: Bearer \$AUTH_TOKEN\" \\"
    echo "  -H \"Content-Type: application/json\" \\"
    echo "  -d '$request_body' \\"
    echo "  \"$API_BASE_URL/hosting/servers/import/$instance_id\""
    echo ""
}

# Main execution
main() {
    echo "=================================================="
    echo "  Achidas Server Details Test"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    check_api_health
    
    if [ $# -eq 0 ]; then
        # No arguments - list all servers
        list_all_servers
        echo ""
        log_info "To get details of a specific server, run:"
        log_info "$0 <instance_id>"
    elif [ "$1" = "list" ]; then
        # List all servers
        list_all_servers
    else
        # Get specific server details
        instance_id=$1
        get_server_details "$instance_id"
        
        # Optionally test import
        if [ $# -eq 2 ]; then
            echo ""
            test_import "$instance_id" "$2"
        fi
    fi
    
    echo ""
    log_info "Rate limiting: API calls are limited to 25 requests per second"
    log_info "All API calls include automatic retry with exponential backoff"
    echo ""
}

# Run main function
main "$@"
