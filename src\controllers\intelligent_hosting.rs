use crate::{
    controllers::{success_response, message_response, Controller<PERSON><PERSON><PERSON>, Controller<PERSON><PERSON><PERSON>},
    models::ApiResponse,
    services::intelligent_hosting::{
        IntelligentHostingService, HostingPlan, UserDeployment, ServerPoolStatus,
        ProfitAnalysis, ScalingAction, PoolType, VultrServerInfo, ServerSetupInfo, Server
    },
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tracing::instrument;

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDeploymentRequest {
    pub plan: String,
    pub region: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProfitAnalysisRequest {
    pub user_distribution: HashMap<String, u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportServerRequest {
    pub pool_type: PoolType,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HostingPlansResponse {
    pub shared_plans: Vec<HostingPlan>,
    pub dedicated_plans: Vec<HostingPlan>,
    pub enterprise_plans: Vec<HostingPlan>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InfrastructureStatusResponse {
    pub server_pools: HashMap<PoolType, ServerPoolStatus>,
    pub total_servers: u32,
    pub total_users: u32,
    pub total_monthly_cost: f64,
    pub average_utilization: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScalingRecommendationsResponse {
    pub actions: Vec<ScalingAction>,
    pub estimated_cost_impact: f64,
    pub priority_actions: Vec<ScalingAction>,
}

/// Get all available hosting plans
#[instrument(skip(state))]
pub async fn get_hosting_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<HostingPlansResponse>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let all_plans = hosting_service
        .get_all_hosting_plans()
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to get hosting plans: {}", e)))?;

    let mut shared_plans = Vec::new();
    let mut dedicated_plans = Vec::new();
    let mut enterprise_plans = Vec::new();

    for plan in all_plans {
        match plan.name.as_str() {
            "starter" | "nano" | "micro" | "small" | "business" => shared_plans.push(plan),
            "dedicated_small" | "dedicated_medium" | "dedicated_large" => dedicated_plans.push(plan),
            "enterprise_standard" | "enterprise_premium" => enterprise_plans.push(plan),
            _ => {}
        }
    }

    let response = HostingPlansResponse {
        shared_plans,
        dedicated_plans,
        enterprise_plans,
    };

    Ok(success_response(response))
}

/// List all available Vultr servers for import
#[instrument(skip(state))]
pub async fn list_vultr_servers(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<Vec<VultrServerInfo>>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let servers = hosting_service
        .list_vultr_servers()
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to list Vultr servers: {}", e)))?;

    Ok(success_response(servers))
}

/// Get details of a specific Vultr server
#[instrument(skip(state))]
pub async fn get_server_details(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<VultrServerInfo>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let server_info = hosting_service
        .get_server_details(&instance_id)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get server details: {}", e)))?;

    Ok(success_response(server_info))
}

/// Import an existing Vultr server into the hosting platform
#[instrument(skip(state))]
pub async fn import_server(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Json(request): Json<ImportServerRequest>,
) -> ControllerResult<Json<ApiResponse<Server>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let server = hosting_service
        .import_existing_server(&instance_id, request.pool_type)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to import server: {}", e)))?;

    Ok(success_response(server))
}

/// Get setup instructions for an imported server
#[instrument(skip(state))]
pub async fn get_server_setup(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    Query(params): Query<HashMap<String, String>>,
) -> ControllerResult<Json<ApiResponse<ServerSetupInfo>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let pool_type_str = params.get("pool_type").unwrap_or(&"shared-hot".to_string());
    let pool_type = match pool_type_str.as_str() {
        "shared-hot" => PoolType::SharedHot,
        "shared-cold" => PoolType::SharedCold,
        "dedicated" => PoolType::Dedicated,
        "enterprise" => PoolType::Enterprise,
        _ => PoolType::SharedHot,
    };

    // Get server details first
    let server = hosting_service
        .import_existing_server(&instance_id, pool_type.clone())
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to get server details: {}", e)))?;

    // Generate setup script
    let setup_script = match pool_type {
        PoolType::SharedHot | PoolType::SharedCold => hosting_service.generate_shared_hosting_setup_script().await,
        PoolType::Dedicated => hosting_service.generate_dedicated_setup_script().await,
        PoolType::Enterprise => hosting_service.generate_enterprise_setup_script().await,
    }.map_err(|e| ControllerError::Internal(format!("Failed to generate setup script: {}", e)))?;

    let setup_info = ServerSetupInfo {
        instance_id: instance_id.clone(),
        setup_script,
        ssh_command: format!("ssh root@{}", server.region), // This would be the actual IP
        next_steps: vec![
            "1. SSH into your server using the provided command".to_string(),
            "2. Copy and paste the setup script".to_string(),
            "3. Run the script as root: chmod +x setup.sh && ./setup.sh".to_string(),
            "4. Wait for setup to complete (5-10 minutes)".to_string(),
            "5. Server will be ready to accept user deployments".to_string(),
        ],
    };

    Ok(success_response(setup_info))
}

/// Deploy a user to the intelligent hosting platform
#[instrument(skip(state))]
pub async fn deploy_user(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
    Json(request): Json<CreateDeploymentRequest>,
) -> ControllerResult<Json<ApiResponse<UserDeployment>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let deployment = hosting_service
        .deploy_user(&user_id, &request.plan, &request.region)
        .await
        .map_err(|e| ControllerError::ExternalApi(format!("Failed to deploy user: {}", e)))?;

    Ok(success_response(deployment))
}

/// Get infrastructure status
#[instrument(skip(state))]
pub async fn get_infrastructure_status(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<InfrastructureStatusResponse>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let server_pools = hosting_service
        .get_server_pools_status()
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to get server pools status: {}", e)))?;

    let total_servers = server_pools.values().map(|p| p.total_servers).sum();
    let total_users = server_pools.values().map(|p| p.total_users).sum();
    let total_monthly_cost = server_pools.values().map(|p| p.monthly_cost).sum();
    let average_utilization = server_pools.values()
        .map(|p| (p.average_cpu_utilization + p.average_memory_utilization) / 2.0)
        .sum::<f64>() / server_pools.len().max(1) as f64;

    let response = InfrastructureStatusResponse {
        server_pools,
        total_servers,
        total_users,
        total_monthly_cost,
        average_utilization,
    };

    Ok(success_response(response))
}

/// Calculate profit analysis
#[instrument(skip(state))]
pub async fn calculate_profit_analysis(
    State(state): State<Arc<AppState>>,
    Json(request): Json<ProfitAnalysisRequest>,
) -> ControllerResult<Json<ApiResponse<ProfitAnalysis>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let analysis = hosting_service
        .calculate_profit_analysis(&request.user_distribution)
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to calculate profit analysis: {}", e)))?;

    Ok(success_response(analysis))
}

/// Get auto-scaling recommendations
#[instrument(skip(state))]
pub async fn get_scaling_recommendations(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<ScalingRecommendationsResponse>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let actions = hosting_service
        .auto_scale_check()
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to get scaling recommendations: {}", e)))?;

    let estimated_cost_impact: f64 = actions.iter().map(|a| a.estimated_cost).sum();
    let priority_actions = actions.iter()
        .filter(|a| a.estimated_cost.abs() > 5.0) // Actions with significant cost impact
        .cloned()
        .collect();

    let response = ScalingRecommendationsResponse {
        actions,
        estimated_cost_impact,
        priority_actions,
    };

    Ok(success_response(response))
}

/// Get specific hosting plan details
#[instrument(skip(state))]
pub async fn get_hosting_plan(
    State(state): State<Arc<AppState>>,
    Path(plan_name): Path<String>,
) -> ControllerResult<Json<ApiResponse<HostingPlan>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let plan = hosting_service
        .get_hosting_plan(&plan_name)
        .map_err(|e| ControllerError::NotFound(format!("Hosting plan not found: {}", e)))?;

    Ok(success_response(plan))
}

/// Execute auto-scaling action
#[instrument(skip(state))]
pub async fn execute_scaling_action(
    State(state): State<Arc<AppState>>,
    Json(action): Json<ScalingAction>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // In a real implementation, this would execute the scaling action
    // For now, just return success
    Ok(message_response(format!(
        "Scaling action {:?} scheduled for execution on pool {:?}",
        action.action_type, action.pool_type
    )))
}

/// Get African market pricing for a specific country
#[instrument(skip(state))]
pub async fn get_african_pricing(
    State(state): State<Arc<AppState>>,
    Path(country_code): Path<String>,
) -> ControllerResult<Json<ApiResponse<HashMap<String, f64>>>> {
    let hosting_service = IntelligentHostingService::new(
        state.vultr_client.clone(),
        state.config.clone(),
    );

    let all_plans = hosting_service
        .get_all_hosting_plans()
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to get hosting plans: {}", e)))?;

    let mut pricing = HashMap::new();
    for plan in all_plans {
        if let Some(local_price) = plan.local_pricing.get(&country_code) {
            pricing.insert(plan.name, *local_price);
        } else {
            // Fallback to USD pricing
            pricing.insert(plan.name, plan.monthly_price_usd);
        }
    }

    Ok(success_response(pricing))
}

/// Health check for intelligent hosting service
#[instrument(skip(state))]
pub async fn health_check(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<ApiResponse<HashMap<String, String>>>> {
    let mut health_status = HashMap::new();
    health_status.insert("service".to_string(), "intelligent_hosting".to_string());
    health_status.insert("status".to_string(), "healthy".to_string());
    health_status.insert("version".to_string(), "1.0.0".to_string());
    health_status.insert("timestamp".to_string(), chrono::Utc::now().to_rfc3339());

    Ok(success_response(health_status))
}
