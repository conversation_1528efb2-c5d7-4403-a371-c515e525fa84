fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<LinkApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {link_application}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::Json<LinkApplicationRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {link_application}: Handler<_, _>
