fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {get_application}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::ApplicationResponse>>, ControllerError>> {get_application}: Handler<_, _>
