{"rustc": 2830703817519440116, "features": "[\"async-runtime\", \"default\", \"http-listener\", \"hyper\", \"hyper-tls\", \"ipnet\", \"push-gateway\", \"tokio\", \"tracing\"]", "declared_features": "[\"async-runtime\", \"default\", \"http-listener\", \"hyper\", \"hyper-tls\", \"ipnet\", \"push-gateway\", \"tokio\", \"tracing\"]", "target": 2221640769694198083, "profile": 15657897354478470176, "path": 13301641855495304115, "deps": [[95042085696191081, "ipnet", false, 14225013059394677478], [4801984952432540513, "metrics", false, 16267042166317811839], [7414427314941361239, "hyper", false, 15474622988467499590], [8008191657135824715, "thiserror", false, 3265526716396010181], [8606274917505247608, "tracing", false, 17320339988540626530], [9108455738564554921, "quanta", false, 1138047484631683897], [9538054652646069845, "tokio", false, 8568442550004330865], [12367227501898450486, "hyper_tls", false, 3435100102515184031], [14483812548788871374, "indexmap", false, 11578274163333973057], [15157431968146737110, "metrics_util", false, 4938052717441923235], [18066890886671768183, "base64", false, 8310951401715748559]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\metrics-exporter-prometheus-f672d26aa24f44d5\\dep-lib-metrics_exporter_prometheus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}