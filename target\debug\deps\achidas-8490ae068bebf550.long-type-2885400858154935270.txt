fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::J<PERSON><ApiResponse<()>>, ControllerError>> {delete_secret}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::J<PERSON><ApiResponse<()>>, ControllerError>> {delete_secret}: Handler<_, _>
