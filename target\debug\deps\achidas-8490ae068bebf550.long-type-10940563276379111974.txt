fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::build::BuildLogsResponse>>, ControllerError>> {get_build_logs}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, Query<LogsQuery>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::build::BuildLogsResponse>>, ControllerError>> {get_build_logs}: Handler<_, _>
