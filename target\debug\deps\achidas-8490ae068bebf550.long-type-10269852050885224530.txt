fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::DeploymentResponse>>, ControllerError>> {get_deployment}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<(std::string::String, std::string::String)>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::deployment::DeploymentResponse>>, ControllerError>> {get_deployment}: Handler<_, _>
