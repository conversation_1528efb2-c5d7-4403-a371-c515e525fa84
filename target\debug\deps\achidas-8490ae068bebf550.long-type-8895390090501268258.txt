fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::<PERSON><PERSON><SetSecretRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {set_secret}
fn(axum::extract::State<Arc<{type error}, {type error}>>, {type error}, axum::extract::Path<std::string::String>, axum::J<PERSON><SetSecretRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<()>>, ControllerError>> {set_secret}: Handler<_, _>
